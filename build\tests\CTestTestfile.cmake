# CMake generated Testfile for 
# Source directory: C:/PROJECT/HighJump/tests
# Build directory: C:/PROJECT/HighJump/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;80;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;80;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;80;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;80;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[DatabaseManagerTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;81;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;81;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;81;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;81;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[ConfigManagerTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;82;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;82;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;82;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;82;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[ApiClientTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[E2EWorkflowTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_e2e_workflow.exe")
  set_tests_properties([=[E2EWorkflowTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[E2EWorkflowTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_e2e_workflow.exe")
  set_tests_properties([=[E2EWorkflowTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[E2EWorkflowTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_e2e_workflow.exe")
  set_tests_properties([=[E2EWorkflowTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[E2EWorkflowTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_e2e_workflow.exe")
  set_tests_properties([=[E2EWorkflowTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[E2EWorkflowTest]=] NOT_AVAILABLE)
endif()
