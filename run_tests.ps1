# High Jump Competition Management System - Test Runner Script
# This script runs unit tests with proper Qt environment setup

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "High Jump Competition Management System" -ForegroundColor Cyan
Write-Host "Test Runner with Qt Environment Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Store original location
$originalLocation = Get-Location

# Check if build directory exists
if (-not (Test-Path "build")) {
    Write-Host "✗ ERROR: Build directory not found" -ForegroundColor Red
    Write-Host "Please run build.ps1 first to build the project" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Set up Qt environment for tests
$testDir = "build\bin\Release"

# Change to Release directory where Qt DLLs are located
if (Test-Path $testDir) {
    Set-Location $testDir
    Write-Host "✓ Changed to Release directory: $(Get-Location)" -ForegroundColor Green

    # Set Qt plugin path to current directory
    $env:QT_PLUGIN_PATH = $PWD.Path
    $env:QT_DEBUG_PLUGINS = "0"
    Write-Host "✓ Set Qt Plugin Path: $env:QT_PLUGIN_PATH" -ForegroundColor Green

    # Check for required Qt SQLite files
    $qtFiles = @("qsqlite.dll", "sqldrivers\qsqlite.dll")
    foreach ($file in $qtFiles) {
        if (Test-Path $file) {
            Write-Host "✓ Found Qt file: $file" -ForegroundColor Green
        } else {
            Write-Host "⚠ WARNING: Qt file not found: $file" -ForegroundColor Yellow
        }
    }
    Write-Host ""
} else {
    Write-Host "✗ ERROR: Release directory not found: $testDir" -ForegroundColor Red
    Write-Host "Please build the project in Release mode first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Define test executables
$testFiles = @(
    "test_database_manager.exe",
    "test_config_manager.exe",
    "test_api_client.exe",
    "test_e2e_workflow.exe"
)

Write-Host "Running unit tests..." -ForegroundColor Cyan
Write-Host ""

$allTestsPassed = $true

foreach ($testFile in $testFiles) {
    if (Test-Path $testFile) {
        Write-Host "Running $testFile..." -ForegroundColor Yellow
        try {
            # Run test executable directly from Release directory
            $process = Start-Process -FilePath ".\$testFile" -Wait -PassThru -NoNewWindow
            if ($process.ExitCode -eq 0) {
                Write-Host "✓ $testFile passed" -ForegroundColor Green
            } else {
                Write-Host "✗ $testFile failed with exit code $($process.ExitCode)" -ForegroundColor Red
                $allTestsPassed = $false
            }
        } catch {
            Write-Host "✗ $testFile failed with exception: $($_.Exception.Message)" -ForegroundColor Red
            $allTestsPassed = $false
        }
        Write-Host ""
    } else {
        Write-Host "⚠ WARNING: $testFile not found in current directory" -ForegroundColor Yellow
        $allTestsPassed = $false
    }
}

# Restore original location
Set-Location $originalLocation

Write-Host "========================================" -ForegroundColor Cyan
if ($allTestsPassed) {
    Write-Host "✓ All tests passed!" -ForegroundColor Green
    Write-Host "High Jump Competition Management System tests completed successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Some tests failed" -ForegroundColor Red
    Write-Host "Please check the test output above for details" -ForegroundColor Yellow
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "- Qt SQLite plugin not found (check sqldrivers folder)" -ForegroundColor Gray
    Write-Host "- Missing Qt DLLs in Release directory" -ForegroundColor Gray
    Write-Host "- Database file permissions or path issues" -ForegroundColor Gray
}
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit"